## 2025-06-04
添加场站应付款统计表
```sql
CREATE TABLE `order_stations_state` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `station_id` bigint(24) NOT NULL COMMENT '场站id',
  `station_name` varchar(255) DEFAULT NULL COMMENT '场站名称',
  `corp_id` bigint(24) NOT NULL COMMENT '运营商id',
  `electricity_total` int(11) NOT NULL DEFAULT '0' COMMENT '电量',
  `electricity_price` int(11) NOT NULL DEFAULT '0' COMMENT '电价 - 保留4位小数',
  `service_price` int(11) NOT NULL COMMENT '服务费 - 保留4位小数',
  `range_time` varchar(20) NOT NULL DEFAULT '1' COMMENT '统计时间 如2025年1月-3月,跨年表示:2024年12月-2025年3月',
  `ratio_ser_price` int(11) NOT NULL DEFAULT '80' COMMENT '服务费新能分成百分比',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` int(11) DEFAULT '0' COMMENT '0未删除，1已删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
```

## 2025-05-28
优化文件存储
ALTER TABLE `file`
ADD INDEX `sha1_unique`(`sha1`) USING BTREE COMMENT '相同的图片不重复存储';

## 2025-05-19
更新订单表注释
```sql
ALTER TABLE `order` 
MODIFY COLUMN `pay_mode` tinyint(2) UNSIGNED NOT NULL DEFAULT 3 COMMENT '支付方式：1微信支付分、2支付宝、3零钱、4会员卡' AFTER `type`;
```

## 2025-4-15

修改订单表discount字段类型，提高精度
```sql
ALTER TABLE `order` 
MODIFY COLUMN `discount` decimal(11, 2) UNSIGNED NOT NULL DEFAULT 100 COMMENT '优惠折扣(整数部分精确到小数点后1位)29.4表示2.94折' AFTER `reservation_time`
```

添加附加费字段
```sql
ALTER TABLE `order` 
ADD COLUMN `surcharge` int(11) NOT NULL DEFAULT 0 COMMENT '附加费(精确小数点后5位,不参与折扣)' AFTER `ser_price`;

ALTER TABLE `tariff_group`
    ADD COLUMN `surcharge` int(11) NULL DEFAULT 0 COMMENT '附加费(精确到小数点后5位,不参与折扣)' AFTER `valley_ser_fee`;

ALTER TABLE `order`
    MODIFY COLUMN `ser_price` int(11) NOT NULL DEFAULT 0 COMMENT '本次服务费用(这是打折后的服务费用)+附加费 - 保留4为小数' AFTER `electricity_price`
```

## 2025-3-21

修改订单表的discount字段，将其数据类型修改为int(11),提高上限
```sql
ALTER TABLE `order` 
MODIFY COLUMN `discount` int(11) UNSIGNED NOT NULL DEFAULT 100 COMMENT '优惠折扣(精确到小数点后1位)' AFTER `reservation_time`
```