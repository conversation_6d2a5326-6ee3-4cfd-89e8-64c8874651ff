<?php

namespace app\common\model;

use think\Model;

/**
 * 场站清分付款统计模型
 */
class OrderStationsState extends Model
{
    protected $table = 'order_stations_state';

    /**
     * 创建场站清分统计记录
     *
     * @param array $data
     * @return bool
     */
    public function createStationState(array $data): bool
    {
        return $this->insert($data) > 0;
    }

    /**
     * 批量创建场站清分统计记录
     *
     * @param array $dataList
     * @return bool
     */
    public function createStationStates(array $dataList): bool
    {
        return $this->insertAll($dataList) > 0;
    }
}
