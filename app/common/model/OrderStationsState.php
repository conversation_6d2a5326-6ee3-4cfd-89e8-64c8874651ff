<?php

namespace app\common\model;

use think\Model;
use think\model\concern\SoftDelete;

class OrderStationsState extends Model
{
    protected $table = 'order_stations_state';

    /**
     * 状态: 未删除
     */
    public const IS_DEL_NOT = 0;

    /**
     * 状态: 已删除
     */
    public const IS_DEL_YES = 1;

    /**
     * 获取场站状态列表
     * @param array $condition
     * @param int $page
     * @param int $limit
     * @return array
     */
    public static function getList(array $condition = [], int $page = 1, int $limit = 10): array
    {
        $query = self::withTrashed(false);

        if (isset($condition['station_name']) && $condition['station_name']) {
            $query->where('station_name', 'like', "%{$condition['station_name']}%");
        }

        if (isset($condition['corp_id']) && $condition['corp_id']) {
            $query->where('corp_id', $condition['corp_id']);
        }

        $result = $query->order('create_time', 'desc')->paginate(['list_rows' => $limit, 'page' => $page]);

        return [
            'data' => $result->items(),
            'total' => $result->total(),
        ];
    }

    /**
     * 更新场站状态
     * @param array $data
     */
    public function updateStationState($id,array $data)
    {
        return $this->where('id', $id)->update($data);
    }

    /**
     * 删除场站状态（软删除）
     */
    public function deleteStationState()
    {
        return $this->update(['is_del' => self::IS_DEL_YES]);
    }

    /**
     * 关联场站
     */
    public function station()
    {
        return $this->hasOne(Stations::class, 'id', 'station_id');
    }

    /**
     * 关联运营商
     */
    public function corp()
    {
        return $this->hasOne(Corp::class, 'id', 'corp_id');
    }
}