<?php

namespace app\admin\controller;

use app\common\lib\exception\RuntimeException;
use app\common\lib\VerifyData;
use app\common\model\OrderStationsState as OrderStationsStateModel;
use hg\apidoc\annotation as Apidoc;
use Respect\Validation\Validator as v;
use think\response\Json;

#[Apidoc\Title("场站状态管理")]
class OrderStationsState extends BaseController
{

    private OrderStationsStateModel $modelClass;

    public function initialize(): void
    {
        $this->modelClass = new OrderStationsStateModel();
    }

    #[
        Apidoc\Title("获取场站状态列表"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/order_stations_state/list"),
        Apidoc\Param(name: "page", type: "int", require: true, desc: "页码"),
        Apidoc\Param(name: "limit", type: "int", require: true, desc: "每页数量"),
        Apidoc\Param(name: "station_name", type: "string", desc: "场站名称"),
        Apidoc\Param(name: "corp_id", type: "int", desc: "运营商id"),
        Apidoc\Returned(name: "list", type: "array", desc: "数据列表"),
        Apidoc\Returned(name: "total", type: "int", desc: "总数")
    ]
    public function list(): Json
    {
        $data = $this->request->post();
        $page = $data['page'] ?? 1;
        $limit = $data['limit'] ?? 10;

        $condition = [];
        if (isset($data['station_name'])) {
            $condition['station_name'] = $data['station_name'];
        }
        if (isset($data['corp_id'])) {
            $condition['corp_id'] = $data['corp_id'];
        }

        $list = $this->modelClass->getList($condition, $page, $limit);

        return $this->res_success($list);
    }

    #[
        Apidoc\Title("更新场站状态"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/order_stations_state/update"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "记录ID"),
        Apidoc\Param(name: "electricity_total", type: "int", desc: "电量"),
        Apidoc\Param(name: "electricity_price", type: "int", desc: "电价 - 保留4位小数"),
        Apidoc\Param(name: "service_price", type: "int", desc: "服务费 - 保留4位小数"),
        Apidoc\Param(name: "ratio_ser_price", type: "int", desc: "服务费新能分成百分比"),
    ]
    public function update(): Json
    {
        return $this->openExceptionCatch(function () {
            $data = $this->request->post();
            // 验证数据
            $verifyData = v::input($data, VerifyData::stations_state_update([
                'id',
                'electricity_total',
                'electricity_price',
                'service_price',
                'ratio_ser_price'
            ]));

            $record = $this->modelClass->find($verifyData['id']);
            if (!$record) {
                throw new RuntimeException("记录不存在", [], RuntimeException::CodeBusinessException);
            }

            $this->modelClass->updateStationState($verifyData['id'], $verifyData);

            return $this->res_success();
        });
    }

    #[
        Apidoc\Title("删除场站状态"),
        Apidoc\Method("POST"),
        Apidoc\Url("/admin/order_stations_state/delete"),
        Apidoc\Param(name: "id", type: "int", require: true, desc: "记录ID"),
    ]
    public function delete(): Json
    {
        $data = $this->request->post();

        if (!isset($data['id'])) {
            throw new RuntimeException("缺少必要参数: id", [], RuntimeException::CodeBusinessException);
        }

        $record = $this->modelClass->find($data['id']);
        if (!$record) {
            throw new RuntimeException("记录不存在", [], RuntimeException::CodeBusinessException);
        }

       $this->modelClass->deleteStationState();

        return $this->res_success();
    }
}